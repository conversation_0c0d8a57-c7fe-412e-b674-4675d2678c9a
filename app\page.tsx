"use client";

import InteractiveAvatar from "@/components/InteractiveAvatar";
import { LoginPage } from "@/components/LoginPage";
import { useAuth } from "@/components/logic/useAuth";

export default function App() {
  const { user, error, login, logout, isAuthenticated } = useAuth();

  if (!isAuthenticated) {
    return <LoginPage onLogin={login} error={error} />;
  }

  return (
    <div className="w-screen h-screen flex flex-col">
      <div className="w-[900px] flex flex-col items-start justify-start gap-5 mx-auto pt-4 pb-20">
        <div className="w-full flex justify-between items-center mb-4">
          <h2 className="text-xl text-white">Welcome, {user?.username}!</h2>
          <button
            onClick={logout}
            className="text-gray-400 hover:text-white text-sm"
          >
            Sign Out
          </button>
        </div>
        <div className="w-full">
          <InteractiveAvatar />
        </div>
      </div>
    </div>
  );
}

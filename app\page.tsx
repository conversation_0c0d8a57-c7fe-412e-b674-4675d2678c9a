"use client";

import InteractiveAvatar from "@/components/InteractiveAvatar";
import { LoginPage } from "@/components/LoginPage";
import { useAuth } from "@/components/logic/useAuth";

export default function App() {
  const { error, loading, login, isAuthenticated, isInitialized } = useAuth();

  // Show loading while initializing auth state
  if (!isInitialized) {
    return (
      <div
        className="min-h-screen flex align-items-center justify-content-center"
        style={{ backgroundColor: "#f8f9fa" }}
      >
        <div style={{ color: "#333333" }}>Loading...</div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <LoginPage onLogin={login} error={error} loading={loading} />;
  }

  return (
    <div
      className="w-full h-full flex flex-column"
      style={{ backgroundColor: "#f8f9fa", minHeight: "100vh" }}
    >
      <div className="w-full max-w-6xl flex flex-column gap-4 mx-auto p-4">
        <div className="w-full">
          <InteractiveAvatar />
        </div>
      </div>
    </div>
  );
}

"use client";

import InteractiveAvatar from "@/components/InteractiveAvatar";
import { LoginPage } from "@/components/LoginPage";
import { useAuth } from "@/components/logic/useAuth";

export default function App() {
  const {
    user,
    error,
    loading,
    login,
    logout,
    isAuthenticated,
    isInitialized,
  } = useAuth();

  // Show loading while initializing auth state
  if (!isInitialized) {
    return (
      <div className="min-h-screen flex align-items-center justify-content-center">
        <div>Loading...</div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <LoginPage onLogin={login} error={error} loading={loading} />;
  }

  return (
    <div className="w-full h-full flex flex-column">
      <div className="w-full max-w-6xl flex flex-column gap-4 mx-auto p-4">
        <div className="w-full flex justify-content-between align-items-center mb-3">
          <h2 className="text-xl m-0" style={{ color: "#ffffff" }}>
            Welcome, {user?.username}!
          </h2>
          <button
            onClick={logout}
            className="p-2 border-none bg-transparent cursor-pointer text-sm"
            style={{ color: "#9ca3af" }}
            onMouseEnter={(e) =>
              ((e.target as HTMLButtonElement).style.color = "#ffffff")
            }
            onMouseLeave={(e) =>
              ((e.target as HTMLButtonElement).style.color = "#9ca3af")
            }
          >
            Sign Out
          </button>
        </div>
        <div className="w-full">
          <InteractiveAvatar />
        </div>
      </div>
    </div>
  );
}

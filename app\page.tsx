"use client";

import InteractiveAvatar from "@/components/InteractiveAvatar";
import { LoginPage } from "@/components/LoginPage";
import { useAuth } from "@/components/logic/useAuth";

export default function App() {
  const { error, loading, login, isAuthenticated, isInitialized } = useAuth();

  // Show loading while initializing auth state
  if (!isInitialized) {
    return (
      <div
        className="min-h-screen flex align-items-center justify-content-center"
        style={{ backgroundColor: "var(--bg-secondary)" }}
      >
        <div
          className="flex flex-column align-items-center"
          style={{ gap: "var(--space-4)" }}
        >
          <div className="p-progress-spinner" />
          <span
            className="text-body-medium"
            style={{ color: "var(--text-secondary)" }}
          >
            Loading your experience...
          </span>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <LoginPage onLogin={login} error={error} loading={loading} />;
  }

  return (
    <div
      className="w-full h-full flex flex-column"
      style={{
        backgroundColor: "var(--bg-secondary)",
        minHeight: "calc(100vh - 5rem)", // Account for navbar height
      }}
    >
      <div
        className="w-full max-w-7xl flex flex-column mx-auto"
        style={{
          gap: "var(--space-8)",
          padding: "var(--space-6) 0",
        }}
      >
        <div className="w-full">
          <InteractiveAvatar />
        </div>
      </div>
    </div>
  );
}

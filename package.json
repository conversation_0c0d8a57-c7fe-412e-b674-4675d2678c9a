{"name": "next-app-template", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint . --ext .ts,.tsx,.js,.jsx"}, "dependencies": {"@heygen/streaming-avatar": "^2.0.13", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-toggle-group": "^1.1.3", "ahooks": "^3.8.4", "next": "^15.3.0", "openai": "^4.52.1", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/node": "20.5.7", "@types/react": "^19.0.1", "@types/react-dom": "^19.1.2", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.31.0", "@next/eslint-plugin-next": "^15.3.1", "autoprefixer": "10.4.19", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unused-imports": "^4.1.4", "postcss": "8.4.38", "tailwindcss": "^3.4.17", "typescript": "5.0.4"}}
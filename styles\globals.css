/* PrimeReact Theme - Using light theme to match Figma */
@import 'primereact/resources/themes/saga-blue/theme.css';
@import 'primereact/resources/primereact.min.css';
@import 'primeicons/primeicons.css';
@import 'primeflex/primeflex.css';

/* Global Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body {
  font-family: var(--font-sans), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  background-color: #f8f9fa;
  color: #333333;
  min-height: 100vh;
}

body {
  background: #f8f9fa;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Custom theme colors to match Figma design */
:root {
  --primary-color: #007bff;
  --primary-color-hover: #0056b3;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --border-color: #dee2e6;
  --text-color: #333333;
  --text-muted: #6c757d;
  --background-color: #ffffff;
  --sidebar-bg: #f8f9fa;
}

/* PrimeReact component overrides for light theme */
.p-component {
  font-family: var(--font-sans), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

.p-inputtext {
  background-color: #ffffff !important;
  border-color: #ced4da !important;
  color: #333333 !important;
  border-radius: 6px !important;
  padding: 0.75rem !important;
}

.p-inputtext:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

.p-button {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  border-radius: 6px !important;
  padding: 0.75rem 1.5rem !important;
  font-weight: 500 !important;
}

.p-button:hover {
  background-color: var(--primary-color-hover) !important;
  border-color: var(--primary-color-hover) !important;
}

.p-dropdown {
  background-color: #ffffff !important;
  border-color: #ced4da !important;
  color: #333333 !important;
  border-radius: 6px !important;
}

.p-dropdown:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

.p-dropdown-panel {
  background-color: #ffffff !important;
  border-color: #ced4da !important;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.p-dropdown-item {
  color: #333333 !important;
}

.p-dropdown-item:hover {
  background-color: #f8f9fa !important;
}

.p-dropdown-item.p-highlight {
  background-color: var(--primary-color) !important;
  color: #ffffff !important;
}

.p-card {
  background-color: #ffffff !important;
  border: 1px solid #dee2e6 !important;
  border-radius: 8px !important;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
  color: #333333 !important;
}

.p-toolbar {
  background-color: #ffffff !important;
  border: none !important;
  border-bottom: 1px solid #dee2e6 !important;
}

.p-selectbutton .p-button {
  background-color: #ffffff !important;
  border-color: #ced4da !important;
  color: #333333 !important;
}

.p-selectbutton .p-button.p-highlight {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: #ffffff !important;
}

.p-password input {
  background-color: #ffffff !important;
  border-color: #ced4da !important;
  color: #333333 !important;
  border-radius: 6px !important;
  padding: 0.75rem !important;
}

.p-message {
  border-radius: 6px !important;
}

li {
  list-style-type: square;
  margin-left: 30px;
  padding: 2px;
}
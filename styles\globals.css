/* PrimeReact Theme */
@import 'primereact/resources/themes/vela-blue/theme.css';
@import 'primereact/resources/primereact.min.css';
@import 'primeicons/primeicons.css';
@import 'primeflex/primeflex.css';

/* Global Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body {
  font-family: var(--font-sans), -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
  background-color: #0f0f0f;
  color: #ffffff;
  min-height: 100vh;
}

body {
  background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: #444;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #666;
}

/* PrimeReact component overrides for dark theme */
.p-component {
  font-family: var(--font-sans), -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
}

.p-inputtext {
  background-color: #3f3f46 !important;
  border-color: #52525b !important;
  color: #ffffff !important;
}

.p-inputtext:focus {
  border-color: #7559FF !important;
  box-shadow: 0 0 0 0.2rem rgba(117, 89, 255, 0.2) !important;
}

.p-button {
  background-color: #7559FF !important;
  border-color: #7559FF !important;
}

.p-button:hover {
  background-color: #6366f1 !important;
  border-color: #6366f1 !important;
}

.p-dropdown {
  background-color: #3f3f46 !important;
  border-color: #52525b !important;
  color: #ffffff !important;
}

.p-dropdown:focus {
  border-color: #7559FF !important;
  box-shadow: 0 0 0 0.2rem rgba(117, 89, 255, 0.2) !important;
}

.p-dropdown-panel {
  background-color: #3f3f46 !important;
  border-color: #52525b !important;
}

.p-dropdown-item {
  color: #ffffff !important;
}

.p-dropdown-item:hover {
  background-color: #52525b !important;
}

.p-dropdown-item.p-highlight {
  background-color: #7559FF !important;
}

.p-card {
  background-color: #1f1f1f !important;
  border-color: #374151 !important;
  color: #ffffff !important;
}

.p-menubar {
  background-color: #1f1f1f !important;
  border-color: #374151 !important;
}

.p-menubar .p-menuitem-link {
  color: #ffffff !important;
}

.p-menubar .p-menuitem-link:hover {
  background-color: #374151 !important;
}

li {
  list-style-type: square;
  margin-left: 30px;
  padding: 2px;
}
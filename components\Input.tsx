import React from "react";
import { InputText } from "primereact/inputtext";

interface InputProps {
  value: string | undefined | null;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  type?: string;
  size?: "small" | "large" | null;
}

export const Input = (props: InputProps) => {
  return (
    <InputText
      className={`w-full ${props.className || ""}`}
      placeholder={props.placeholder}
      type={props.type || "text"}
      value={props.value || ""}
      onChange={(e) => props.onChange(e.target.value)}
      disabled={props.disabled}
      size={props.size}
    />
  );
};

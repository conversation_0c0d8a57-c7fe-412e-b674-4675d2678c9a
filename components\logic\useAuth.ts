"use client";

import { useState, useCallback } from "react";

export interface User {
  username: string;
}

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [error, setError] = useState<string>("");

  const login = useCallback(async (username: string, password: string) => {
    setError("");
    
    // Simple demo authentication - in a real app, this would be an API call
    if (username === "demo" && password === "password") {
      setUser({ username });
      return true;
    } else {
      setError("Invalid username or password");
      return false;
    }
  }, []);

  const logout = useCallback(() => {
    setUser(null);
    setError("");
  }, []);

  const isAuthenticated = user !== null;

  return {
    user,
    error,
    login,
    logout,
    isAuthenticated,
  };
};

"use client";

import { useState, useCallback, useEffect } from "react";
import { tokenStorage, TokenData } from "./tokenStorage";
import { useApiPost } from "./useApi";
import { LoginRequest, LoginResponse } from "./apiConfig";

export interface User {
  id?: string;
  username: string;
  email?: string;
}

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  const loginApi = useApiPost<LoginResponse>("/Mobile/Authentication");

  // Initialize auth state from stored token
  useEffect(() => {
    const initializeAuth = () => {
      const tokenData = tokenStorage.getToken();
      if (tokenData && tokenData.accessToken) {
        // In a real app, you might want to validate the token with the server
        // For now, we'll assume the stored token is valid
        setUser({ username: "User" }); // You might store user data with the token
      }
      setIsInitialized(true);
    };

    initializeAuth();
  }, []);

  const login = useCallback(
    async (username: string, password: string) => {
      try {
        console.log("Login attempt for username:", username);

        // Prepare login request with all required fields
        const loginRequest: LoginRequest = {
          username,
          password,
          email: "", // Empty string as requested
          mobile: "", // Empty string as requested
          platform: "", // Empty string as requested
          manufacturer: "", // Empty string as requested
          model: "", // Empty string as requested
          deviceId: "", // Empty string as requested
          isRemember: false, // Default to false
        };

        console.log("Login request payload:", loginRequest);

        const response = await loginApi.execute({
          data: loginRequest,
        });

        console.log("Login API response:", response);

        if (response && response.accessToken) {
          console.log("Login successful, storing token...");

          // Store token securely
          const tokenData: TokenData = {
            accessToken: response.accessToken,
            refreshToken: response.refreshToken,
            tokenType: response.tokenType || "Bearer",
            expiresAt: response.expiresIn
              ? Date.now() + response.expiresIn * 1000
              : undefined,
          };

          tokenStorage.setToken(tokenData);

          // Set user data
          const userData: User = {
            id: response.user?.id,
            username: response.user?.username || username,
            email: response.user?.email,
          };

          setUser(userData);
          console.log("User data set:", userData);
          return true;
        } else {
          console.log("Login failed: No access token in response");
          return false;
        }
      } catch (error) {
        console.error("Login error:", error);
        return false;
      }
    },
    [loginApi]
  );

  const logout = useCallback(() => {
    tokenStorage.removeToken();
    setUser(null);
  }, []);

  const isAuthenticated = user !== null;

  return {
    user,
    error: loginApi.error,
    loading: loginApi.loading,
    login,
    logout,
    isAuthenticated,
    isInitialized,
  };
};

import React, { useEffect, useRef } from "react";

import { useMessageHistory, MessageSender } from "../logic";

export const MessageHistory: React.FC = () => {
  const { messages } = useMessageHistory();
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = containerRef.current;

    if (!container || messages.length === 0) return;

    container.scrollTop = container.scrollHeight;
  }, [messages]);

  return (
    <div
      ref={containerRef}
      className="overflow-y-auto flex flex-column gap-2 px-4 py-3 align-self-center border-round-lg shadow-2"
      style={{
        width: "600px",
        maxHeight: "150px",
        color: "#333333",
        backgroundColor: "#ffffff",
        border: "1px solid #dee2e6",
      }}
    >
      {messages.map((message) => (
        <div
          key={message.id}
          className={`flex flex-column gap-1 ${
            message.sender === MessageSender.CLIENT
              ? "align-self-end align-items-end"
              : "align-self-start align-items-start"
          }`}
          style={{ maxWidth: "350px" }}
        >
          <p className="text-xs font-medium" style={{ color: "#6c757d" }}>
            {message.sender === MessageSender.AVATAR ? "Avatar" : "You"}
          </p>
          <p
            className="text-sm p-2 border-round"
            style={{
              backgroundColor:
                message.sender === MessageSender.CLIENT ? "#007bff" : "#f8f9fa",
              color:
                message.sender === MessageSender.CLIENT ? "#ffffff" : "#333333",
              border:
                message.sender === MessageSender.CLIENT
                  ? "none"
                  : "1px solid #dee2e6",
            }}
          >
            {message.content}
          </p>
        </div>
      ))}
    </div>
  );
};

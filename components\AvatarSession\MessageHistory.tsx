import React, { useEffect, useRef } from "react";

import { useMessageHistory, MessageSender } from "../logic";

export const MessageHistory: React.FC = () => {
  const { messages } = useMessageHistory();
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = containerRef.current;

    if (!container || messages.length === 0) return;

    container.scrollTop = container.scrollHeight;
  }, [messages]);

  return (
    <div
      ref={containerRef}
      className="overflow-y-auto flex flex-column gap-2 px-2 py-2 align-self-center"
      style={{
        width: "600px",
        maxHeight: "150px",
        color: "#ffffff",
      }}
    >
      {messages.map((message) => (
        <div
          key={message.id}
          className={`flex flex-column gap-1 ${
            message.sender === MessageSender.CLIENT
              ? "align-self-end align-items-end"
              : "align-self-start align-items-start"
          }`}
          style={{ maxWidth: "350px" }}
        >
          <p className="text-xs" style={{ color: "#9ca3af" }}>
            {message.sender === MessageSender.AVATAR ? "Avatar" : "You"}
          </p>
          <p className="text-sm">{message.content}</p>
        </div>
      ))}
    </div>
  );
};

"use client";

import Link from "next/link";
import { Toolbar } from "primereact/toolbar";

import { HeyG<PERSON><PERSON>ogo } from "./Icons";

export default function NavBar() {
  const startContent = (
    <div className="flex align-items-center gap-3">
      <Link href="https://app.heygen.com/" target="_blank">
        <HeyGenLogo />
      </Link>
      <div
        style={{
          background: "linear-gradient(135deg, #87ceeb 0%, #6366f1 100%)",
          WebkitBackgroundClip: "text",
          WebkitTextFillColor: "transparent",
          backgroundClip: "text",
        }}
      >
        <p className="text-xl font-semibold">
          HeyGen Interactive Avatar SDK NextJS Demo
        </p>
      </div>
    </div>
  );

  const endContent = (
    <div className="flex align-items-center gap-3">
      {/* Uncomment these if you want to add navigation links
      <Button
        label="Avatars"
        text
        onClick={() => window.open('https://labs.heygen.com/interactive-avatar', '_blank')}
      />
      <Button
        label="Voices"
        text
        onClick={() => window.open('https://docs.heygen.com/reference/list-voices-v2', '_blank')}
      />
      <Button
        label="API Docs"
        text
        onClick={() => window.open('https://docs.heygen.com/reference/new-session-copy', '_blank')}
      />
      <Button
        label="Guide"
        text
        onClick={() => window.open('https://help.heygen.com/en/articles/9182113-interactive-avatar-101-your-ultimate-guide', '_blank')}
      />
      <Button
        icon={<GithubIcon className="text-default-500" />}
        label="SDK"
        text
        onClick={() => window.open('https://github.com/HeyGen-Official/StreamingAvatarSDK', '_blank')}
      />
      */}
    </div>
  );

  return (
    <Toolbar
      start={startContent}
      end={endContent}
      className="border-none shadow-none"
      style={{
        backgroundColor: "transparent",
        padding: "1.5rem",
        maxWidth: "1000px",
        margin: "0 auto",
      }}
    />
  );
}

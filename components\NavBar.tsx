"use client";

import { Toolbar } from "primereact/toolbar";
import { Button } from "primereact/button";
import { useAuth } from "./logic/useAuth";

export default function NavBar() {
  const { user, logout } = useAuth();

  const handleLogout = () => {
    logout();
  };

  const startContent = (
    <div className="flex align-items-center gap-3">
      <i
        className="pi pi-user-plus"
        style={{ fontSize: "1.5rem", color: "#007bff" }}
      />
      <span
        className="text-xl font-bold"
        style={{ color: "#333333", letterSpacing: "0.5px" }}
      >
        AI Avatar
      </span>
    </div>
  );

  const endContent = (
    <div className="flex align-items-center gap-3">
      <span style={{ color: "#6c757d", fontSize: "0.9rem" }}>
        Welcome, {user?.username || "User"}
      </span>
      <Button
        icon="pi pi-sign-out"
        className="p-button-text p-button-rounded"
        onClick={handleLogout}
        tooltip="Logout"
        style={{
          color: "#dc3545",
          backgroundColor: "transparent",
          border: "1px solid #dc3545",
        }}
      />
    </div>
  );

  return (
    <Toolbar
      start={startContent}
      end={endContent}
      className="border-none"
      style={{
        background: "#ffffff",
        borderBottom: "1px solid #dee2e6",
        padding: "1rem 2rem",
        boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
      }}
    />
  );
}

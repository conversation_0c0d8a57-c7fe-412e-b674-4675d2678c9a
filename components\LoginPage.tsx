"use client";

import React, { useState } from "react";
import { Button } from "./Button";
import { Input } from "./Input";
import { Card } from "primereact/card";
import { Password } from "primereact/password";
import { Message } from "primereact/message";

interface LoginPageProps {
  onLogin: (username: string, password: string) => void;
  error?: string;
}

export const LoginPage: React.FC<LoginPageProps> = ({ onLogin, error }) => {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!username.trim() || !password.trim()) {
      return;
    }

    setIsLoading(true);
    try {
      await onLogin(username, password);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div
      className="min-h-screen flex align-items-center justify-content-center"
      style={{
        background: "linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%)",
      }}
    >
      <Card className="w-full max-w-md p-4">
        <div className="text-center mb-4">
          <h1 className="text-3xl font-bold mb-2" style={{ color: "#ffffff" }}>
            Welcome to AI Avatar
          </h1>
          <p style={{ color: "#9ca3af" }}>
            Sign in to access your interactive avatar
          </p>
        </div>

        <form onSubmit={handleSubmit} className="flex flex-column gap-4">
          <div className="field">
            <label
              htmlFor="username"
              className="block text-sm font-medium mb-2"
              style={{ color: "#d1d5db" }}
            >
              Username
            </label>
            <Input
              value={username}
              onChange={setUsername}
              placeholder="Enter your username"
              className="w-full"
            />
          </div>

          <div className="field">
            <label
              htmlFor="password"
              className="block text-sm font-medium mb-2"
              style={{ color: "#d1d5db" }}
            >
              Password
            </label>
            <Password
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter your password"
              className="w-full"
              feedback={false}
              toggleMask
            />
          </div>

          {error && (
            <Message severity="error" text={error} className="w-full" />
          )}

          <Button
            type="submit"
            disabled={!username.trim() || !password.trim() || isLoading}
            className="w-full"
            loading={isLoading}
          >
            {isLoading ? "Signing in..." : "Sign In"}
          </Button>
        </form>

        <div className="text-center text-sm mt-4" style={{ color: "#9ca3af" }}>
          <p>Demo credentials:</p>
          <p>
            Username: <span style={{ color: "#ffffff" }}>demo</span>
          </p>
          <p>
            Password: <span style={{ color: "#ffffff" }}>password</span>
          </p>
        </div>
      </Card>
    </div>
  );
};

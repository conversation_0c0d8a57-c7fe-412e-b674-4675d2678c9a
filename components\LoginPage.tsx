"use client";

import React, { useState } from "react";
import { Button } from "./Button";
import { Input } from "./Input";
import { Card } from "primereact/card";
import { Password } from "primereact/password";
import { Message } from "primereact/message";

interface LoginPageProps {
  onLogin: (username: string, password: string) => Promise<boolean>;
  error?: string | null;
  loading?: boolean;
}

export const LoginPage: React.FC<LoginPageProps> = ({
  onLogin,
  error,
  loading = false,
}) => {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!username.trim() || !password.trim()) {
      return;
    }

    await onLogin(username, password);
  };

  return (
    <div
      className="min-h-screen flex align-items-center justify-content-center"
      style={{
        background: "#f8f9fa",
        padding: "2rem",
      }}
    >
      <Card className="w-full max-w-md shadow-3">
        <div className="text-center mb-5">
          <h1 className="text-3xl font-bold mb-3" style={{ color: "#333333" }}>
            Welcome to AI Avatar
          </h1>
          <p style={{ color: "#6c757d", fontSize: "1.1rem" }}>
            Sign in to access your interactive avatar
          </p>
        </div>

        <form onSubmit={handleSubmit} className="flex flex-column gap-4">
          <div className="field">
            <label
              htmlFor="username"
              className="block font-medium mb-2"
              style={{ color: "#333333", fontSize: "0.95rem" }}
            >
              Username
            </label>
            <Input
              value={username}
              onChange={setUsername}
              placeholder="Enter your username"
              className="w-full"
              style={{ padding: "0.75rem" }}
            />
          </div>

          <div className="field">
            <label
              htmlFor="password"
              className="block font-medium mb-2"
              style={{ color: "#333333", fontSize: "0.95rem" }}
            >
              Password
            </label>
            <Password
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter your password"
              className="w-full"
              feedback={false}
              toggleMask
              inputStyle={{ padding: "0.75rem" }}
            />
          </div>

          {error && (
            <Message severity="error" text={error} className="w-full" />
          )}

          <Button
            type="submit"
            disabled={!username.trim() || !password.trim() || loading}
            className="w-full mt-3"
            loading={loading}
            style={{
              padding: "0.875rem 1.5rem",
              fontSize: "1rem",
              fontWeight: "500",
            }}
          >
            {loading ? "Signing in..." : "Sign In"}
          </Button>
        </form>

        <div
          className="text-center mt-5"
          style={{ color: "#6c757d", fontSize: "0.9rem" }}
        >
          <p className="mb-2">Demo credentials:</p>
          <p className="mb-1">
            Username:{" "}
            <span style={{ color: "#007bff", fontWeight: "500" }}>demo</span>
          </p>
          <p>
            Password:{" "}
            <span style={{ color: "#007bff", fontWeight: "500" }}>
              password
            </span>
          </p>
        </div>
      </Card>
    </div>
  );
};

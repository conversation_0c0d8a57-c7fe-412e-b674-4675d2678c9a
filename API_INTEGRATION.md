# API Integration Documentation

## Overview

This project has been successfully migrated to PrimeReact and now includes a comprehensive API integration system with secure token storage.

## Features Implemented

### 1. PrimeReact Migration

- ✅ Replaced Tailwind CSS with PrimeReact theme system
- ✅ Updated all UI components to use PrimeReact components
- ✅ Migrated Button, Input, Select, and other form components
- ✅ Updated LoginPage with PrimeReact Card, InputText, Password, and Message components
- ✅ Converted layout and styling to use PrimeFlex classes

### 2. API Integration System

- ✅ Axios-based HTTP client with interceptors
- ✅ Secure token storage with encryption
- ✅ Common hooks for API calls
- ✅ Authentication flow with real API integration

## API Configuration

### Environment Variables

Update `.env.local` with your actual API endpoint:

```
NEXT_PUBLIC_API_BASE_URL=https://your-api-domain.com
```

### Login API Endpoint

The login functionality calls: `POST /Mobile/Authentication`

**Request Payload:**

```json
{
  "username": "user_input",
  "password": "user_input",
  "email": "",
  "mobile": "",
  "platform": "",
  "manufacturer": "",
  "model": "",
  "deviceId": "",
  "isRemember": false
}
```

**Expected Response:**

```json
{
  "accessToken": "jwt_token_here",
  "refreshToken": "refresh_token_here",
  "tokenType": "Bearer",
  "expiresIn": 3600,
  "user": {
    "id": "user_id",
    "username": "username",
    "email": "<EMAIL>"
  }
}
```

## Security Features

### Token Storage

- Tokens are encrypted using XOR encryption before localStorage storage
- Automatic token expiration checking
- Secure token retrieval and cleanup

### API Security

- Automatic Bearer token injection in request headers
- 401 response handling with automatic logout
- Request/response interceptors for error handling

## Usage Examples

### Using the API Hooks

```typescript
import { useApi, useApiPost, useApiGet } from "@/components/logic";

// Generic API call
const { data, loading, error, execute } = useApi();

// Specific HTTP methods
const getUserData = useApiGet("/api/user/profile");
const updateUser = useApiPost("/api/user/update");
```

### Authentication

```typescript
import { useAuth } from "@/components/logic";

const { user, loading, error, login, logout, isAuthenticated } = useAuth();

// Login
const success = await login("username", "password");

// Logout
logout();
```

### Token Management

```typescript
import { tokenStorage } from "@/components/logic";

// Check if user has valid token
const hasToken = tokenStorage.hasValidToken();

// Get access token
const token = tokenStorage.getAccessToken();

// Manual token management
tokenStorage.setToken(tokenData);
tokenStorage.removeToken();
```

## Testing

1. **Login Flow**: Enter credentials in the login form to test API integration
2. **Token Storage**: Check browser localStorage for encrypted token storage
3. **Error Handling**: Test with invalid credentials to see error messages
4. **Auto-logout**: Test token expiration handling

## Development Notes

- Console logging is enabled for debugging API calls
- The application shows loading states during API requests
- Error messages are displayed in the UI using PrimeReact Message component
- Token encryption provides basic security for client-side storage

## Next Steps

1. Update `NEXT_PUBLIC_API_BASE_URL` in `.env.local` with your actual API endpoint
2. Test the login flow with real credentials
3. Customize the user interface as needed
4. Add additional API endpoints using the provided hooks
5. Implement refresh token logic if required by your API

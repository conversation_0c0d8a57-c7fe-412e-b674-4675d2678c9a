import "@/styles/globals.css";
import { Metadata } from "next";
import { Fira_Code as Font<PERSON>ono, Inter as FontS<PERSON> } from "next/font/google";

import NavBar from "@/components/NavBar";

const fontSans = FontSans({
  subsets: ["latin"],
  variable: "--font-sans",
});

const fontMono = FontMono({
  subsets: ["latin"],
  variable: "--font-geist-mono",
});

export const metadata: Metadata = {
  title: {
    default: "HeyGen Interactive Avatar SDK Demo",
    template: `%s - HeyGen Interactive Avatar SDK Demo`,
  },
  icons: {
    icon: "/heygen-logo.png",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html
      suppressHydrationWarning
      className={`${fontSans.variable} ${fontMono.variable}`}
      lang="en"
    >
      <head />
      <body
        className="min-h-screen"
        style={{ backgroundColor: "#f8f9fa", color: "#333333" }}
      >
        <main className="relative flex flex-column h-screen w-screen">
          <NavBar />
          <div className="flex-1" style={{ backgroundColor: "#f8f9fa" }}>
            {children}
          </div>
        </main>
      </body>
    </html>
  );
}
